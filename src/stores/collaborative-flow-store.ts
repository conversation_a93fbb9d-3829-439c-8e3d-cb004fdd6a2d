import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { useShallow } from 'zustand/react/shallow'
import { addEdge, Connection, NodeChange, EdgeChange, applyNodeChanges, applyEdgeChanges } from '@xyflow/react'
import { CustomNode, CustomEdge, CustomNodeData, NodeType, initialNodes, initialEdges } from '@/components/reactflow/node-types'
import { io, Socket } from 'socket.io-client'
import debounce from 'lodash.debounce'
import {
  FlowChangeEvent,
  FlowEventData,
  NodesChangeData,
  EdgesChangeData,
  CursorMoveData,
  FlowViewport
} from '@/lib/websocket'

export interface Participant {
  userId: string
  name: string
  role: 'OWNER' | 'EDITOR' | 'VIEWER'
  cursor?: CursorMoveData
  isActive: boolean
}

// Separate state interface (data only)
interface FlowState {
  nodes: CustomNode[]
  edges: CustomEdge[]
  selectedNodeId: string | null
  nodeIdCounter: number

  // Collaboration state
  roomId: string | null
  isConnected: boolean
  participants: Participant[]
  socket: Socket | null
  isOwner: boolean
  canEdit: boolean
}

// Separate actions interface
interface FlowActions {
  // Original flow actions
  onNodesChange: (changes: NodeChange[]) => void
  onEdgesChange: (changes: EdgeChange[]) => void
  onConnect: (connection: Connection) => void
  addNode: (type: NodeType) => void
  updateNode: (nodeId: string, updates: Partial<CustomNodeData>) => void
  deleteSelectedNodes: () => void
  selectNode: (nodeId: string | null) => void
  clearAll: () => void

  // Collaboration actions
  connectToRoom: (roomId: string, userId: string) => Promise<void>
  disconnectFromRoom: () => void
  updateCursor: (x: number, y: number) => void
  loadRoomData: (flowData: { nodes: CustomNode[]; edges: CustomEdge[]; viewport?: FlowViewport }) => void
  handleRemoteChange: (event: FlowChangeEvent) => void
  setParticipants: (participants: Participant[]) => void
  addParticipant: (participant: Participant) => void
  removeParticipant: (userId: string) => void
  updateParticipantCursor: (userId: string, cursor: CursorMoveData) => void
}

// Cursor queue management (100ms batching, send last position in each batch)
interface CursorQueue {
  x: number
  y: number
  timestamp: number
  lastSentX?: number
  lastSentY?: number
  batchStartTime: number
}

const cursorQueues = new Map<string, CursorQueue>()
const cursorTimers = new Map<string, NodeJS.Timeout>()

function queueCursorUpdate(socket: Socket, roomId: string, x: number, y: number) {
  const currentQueue = cursorQueues.get(roomId)
  const now = Date.now()

  // Check if cursor position has actually changed
  if (currentQueue && currentQueue.lastSentX === x && currentQueue.lastSentY === y) {
    return // No change, don't queue or send
  }

  // Check if we have an active timer (within 100ms batch)
  const hasActiveTimer = cursorTimers.has(roomId)

  if (hasActiveTimer && currentQueue) {
    // We're within an active batch - just update the position (don't reset timer)
    cursorQueues.set(roomId, {
      x,
      y,
      timestamp: now,
      lastSentX: currentQueue.lastSentX,
      lastSentY: currentQueue.lastSentY,
      batchStartTime: currentQueue.batchStartTime
    })
    console.log(`Updated cursor position in active batch for room ${roomId}: (${x}, ${y})`)
  } else {
    // No active timer - start a new batch
    cursorQueues.set(roomId, {
      x,
      y,
      timestamp: now,
      lastSentX: currentQueue?.lastSentX,
      lastSentY: currentQueue?.lastSentY,
      batchStartTime: now
    })

    // Set timer for 100ms to send the last position in this batch
    const timer = setTimeout(() => {
      const cursorData = cursorQueues.get(roomId)

      if (cursorData) {
        // Send the latest cursor position from this batch
        socket.emit('cursor_move', {
          x: cursorData.x,
          y: cursorData.y
        })

        // Update last sent position to track changes
        cursorQueues.set(roomId, {
          ...cursorData,
          lastSentX: cursorData.x,
          lastSentY: cursorData.y
        })

        console.log(`Sent batched cursor update for room ${roomId}: (${cursorData.x}, ${cursorData.y})`)
      }

      // Remove timer reference - batch is complete
      cursorTimers.delete(roomId)
    }, 100) // 100ms batch duration

    cursorTimers.set(roomId, timer)
    console.log(`Started new cursor batch for room ${roomId}: (${x}, ${y})`)
  }
}

function clearCursorQueue(roomId: string) {
  // Clear the timer
  const timer = cursorTimers.get(roomId)
  if (timer) {
    clearTimeout(timer)
    cursorTimers.delete(roomId)
  }

  // Clear any pending cursor data
  cursorQueues.delete(roomId)

  console.log(`Cleared cursor queue for room ${roomId}`)
}

// Create the store with separated state and actions
export const useRemoteCollaborativeFlowStore = create<FlowState & FlowActions>()(
  devtools(
    (set, get) => {
      // Debounced function for sending changes
      const debouncedSendChange = debounce((type: FlowChangeEvent['type'], data: FlowEventData) => {
        const state = get()
        if (state.socket && state.roomId && state.canEdit) {
          state.socket.emit('flow_change', {
            type,
            roomId: state.roomId,
            data
          })
        }
      }, 100) // 100ms debounce

      return {
        // Initial state
        nodes: initialNodes,
        edges: initialEdges,
        selectedNodeId: null,
        nodeIdCounter: 4,

        // Collaboration state
        roomId: null,
        isConnected: false,
        participants: [],
        socket: null,
        isOwner: false,
        canEdit: false,

        // React Flow change handlers with real-time sync
        onNodesChange: (changes) => {
          const state = get()
          const newNodes = applyNodeChanges(changes, state.nodes) as CustomNode[]
          set({ nodes: newNodes }, false, 'onNodesChange')

          // Send change to other participants (debounced)
          if (state.canEdit) {
            debouncedSendChange('nodes_change', { nodes: newNodes } as NodesChangeData)
          }
        },

        onEdgesChange: (changes) => {
          const state = get()
          const newEdges = applyEdgeChanges(changes, state.edges) as CustomEdge[]
          set({ edges: newEdges }, false, 'onEdgesChange')

          // Send change to other participants (debounced)
          if (state.canEdit) {
            debouncedSendChange('edges_change', { edges: newEdges } as EdgesChangeData)
          }
        },

        onConnect: (connection) => {
          const state = get()
          if (!state.canEdit) return

          const newEdges = addEdge(connection, state.edges) as CustomEdge[]
          set({ edges: newEdges }, false, 'onConnect')

          // Send change immediately for connections
          if (state.socket && state.roomId) {
            state.socket.emit('flow_change', {
              type: 'edges_change',
              roomId: state.roomId,
              data: { edges: newEdges } as EdgesChangeData
            })
          }
        },

        addNode: (type) => {
          const state = get()
          if (!state.canEdit) return

          const newNodeId = `node-${state.nodeIdCounter}`
          const newNode: CustomNode = {
            id: newNodeId,
            type,
            position: { x: Math.random() * 400, y: Math.random() * 400 },
            data: {
              label: `${type} Node`,
              description: '',
              ...(type === 'input' && { inputValue: '' }),
              ...(type === 'output' && { outputValue: '' }),
            },
          }

          const newNodes = [...state.nodes, newNode]
          set({
            nodes: newNodes,
            nodeIdCounter: state.nodeIdCounter + 1
          }, false, 'addNode')

          // Send change immediately for new nodes
          if (state.socket && state.roomId) {
            state.socket.emit('flow_change', {
              type: 'nodes_change',
              roomId: state.roomId,
              data: { nodes: newNodes } as NodesChangeData
            })
          }
        },

        updateNode: (nodeId, updates) => {
          const state = get()
          if (!state.canEdit) return

          const newNodes = state.nodes.map(node =>
            node.id === nodeId
              ? { ...node, data: { ...node.data, ...updates } }
              : node
          ) as CustomNode[]

          set({ nodes: newNodes }, false, 'updateNode')

          // Send change (debounced for frequent updates like typing)
          debouncedSendChange('nodes_change', { nodes: newNodes } as NodesChangeData)
        },

        deleteSelectedNodes: () => {
          const state = get()
          if (!state.canEdit || !state.selectedNodeId) return

          const newNodes = state.nodes.filter(node => node.id !== state.selectedNodeId)
          const newEdges = state.edges.filter(edge =>
            edge.source !== state.selectedNodeId && edge.target !== state.selectedNodeId
          )

          set({
            nodes: newNodes,
            edges: newEdges,
            selectedNodeId: null
          }, false, 'deleteSelectedNodes')

          // Send changes immediately for deletions
          if (state.socket && state.roomId) {
            state.socket.emit('flow_change', {
              type: 'nodes_change',
              roomId: state.roomId,
              data: { nodes: newNodes } as NodesChangeData
            })
            state.socket.emit('flow_change', {
              type: 'edges_change',
              roomId: state.roomId,
              data: { edges: newEdges } as EdgesChangeData
            })
          }
        },

        selectNode: (nodeId) => {
          set({ selectedNodeId: nodeId }, false, 'selectNode')
        },

        clearAll: () => {
          const state = get()
          if (!state.canEdit) return

          set({
            nodes: [],
            edges: [],
            selectedNodeId: null,
            nodeIdCounter: 1
          }, false, 'clearAll')

          // Send clear changes
          if (state.socket && state.roomId) {
            state.socket.emit('flow_change', {
              type: 'nodes_change',
              roomId: state.roomId,
              data: { nodes: [] } as NodesChangeData
            })
            state.socket.emit('flow_change', {
              type: 'edges_change',
              roomId: state.roomId,
              data: { edges: [] } as EdgesChangeData
            })
          }
        },

        // Collaboration actions
        connectToRoom: async (roomId: string, userId: string) => {
          const state = get()

          // Disconnect from previous room if any
          if (state.socket) {
            state.socket.disconnect()
          }

          // Create new socket connection
          const socket = io(process.env.NEXT_PUBLIC_WS_URL || window.location.origin, {
            transports: ['websocket', 'polling']
          })

          set({ socket, roomId }, false, 'connectToRoom')

          return new Promise<void>((resolve, reject) => {
            socket.on('connect', () => {
              console.log('Connected to WebSocket server')

              // Join the room
              socket.emit('join_room', { roomId, token: userId })
            })

            socket.on('room_joined', (data: { roomId: string; flowData: any; participants: Participant[] }) => {
              console.log('Joined room:', data.roomId)

              // Load room data
              if (data.flowData) {
                get().loadRoomData(data.flowData)
              }

              // Set participants
              get().setParticipants(data.participants)

              set({
                isConnected: true,
                canEdit: true // Will be updated based on actual permissions
              }, false, 'room_joined')

              resolve()
            })

            socket.on('flow_change', (event: FlowChangeEvent) => {
              get().handleRemoteChange(event)
            })

            socket.on('participant_joined', (participant: Participant) => {
              get().addParticipant(participant)
            })

            socket.on('participant_left', (data: { userId: string }) => {
              get().removeParticipant(data.userId)
            })

            socket.on('cursor_move', (data: { userId: string; cursor: { x: number; y: number } }) => {
              get().updateParticipantCursor(data.userId, data.cursor)
            })

            socket.on('error', (error: any) => {
              console.error('WebSocket error:', error)
              reject(new Error(error.message))
            })

            socket.on('disconnect', () => {
              console.log('Disconnected from WebSocket server')
              set({
                isConnected: false,
                participants: []
              }, false, 'disconnected')
            })
          })
        },

        disconnectFromRoom: () => {
          const state = get()

          // Clear cursor queue before disconnecting
          if (state.roomId) {
            clearCursorQueue(state.roomId)
          }

          if (state.socket) {
            state.socket.disconnect()
          }

          set({
            socket: null,
            roomId: null,
            isConnected: false,
            participants: [],
            canEdit: false,
            isOwner: false
          }, false, 'disconnectFromRoom')
        },

        updateCursor: (x: number, y: number) => {
          const state = get()

          if (state.socket && state.roomId) {
            // Queue cursor position for batched sending (100ms intervals)
            queueCursorUpdate(state.socket, state.roomId, x, y)
          }
        },

        loadRoomData: (flowData: { nodes: CustomNode[]; edges: CustomEdge[]; viewport?: FlowViewport }) => {
          if (flowData) {
            set({
              nodes: flowData.nodes || [],
              edges: flowData.edges || [],
              selectedNodeId: null
            }, false, 'loadRoomData')
          }
        },

        handleRemoteChange: (event: FlowChangeEvent) => {
          // Don't apply our own changes
          const state = get()
          if (event.userId === state.socket?.id) return

          switch (event.type) {
            case 'nodes_change': {
              const data = event.data as NodesChangeData
              set({ nodes: data.nodes }, false, 'remote_nodes_change')
              break
            }
            case 'edges_change': {
              const data = event.data as EdgesChangeData
              set({ edges: data.edges }, false, 'remote_edges_change')
              break
            }
            case 'cursor_move':
              // Cursor moves are handled separately
              break
          }
        },

        setParticipants: (participants: Participant[]) => {
          set({ participants }, false, 'setParticipants')
        },

        addParticipant: (participant: Participant) => {
          const state = get()
          const existingIndex = state.participants.findIndex(p => p.userId === participant.userId)

          if (existingIndex >= 0) {
            // Update existing participant
            const updatedParticipants = [...state.participants]
            updatedParticipants[existingIndex] = participant
            set({ participants: updatedParticipants }, false, 'updateParticipant')
          } else {
            // Add new participant
            set({
              participants: [...state.participants, participant]
            }, false, 'addParticipant')
          }
        },

        removeParticipant: (userId: string) => {
          const state = get()
          set({
            participants: state.participants.filter(p => p.userId !== userId)
          }, false, 'removeParticipant')
        },

        updateParticipantCursor: (userId: string, cursor: CursorMoveData) => {
          const state = get()
          const updatedParticipants = state.participants.map(p =>
            p.userId === userId ? { ...p, cursor } : p
          )
          set({ participants: updatedParticipants }, false, 'updateParticipantCursor')
        }
      }
    },
    { name: 'flow-store' }
  )
)

// Hooks for better component integration
export const useRemoteCollaborativeFlowActions = () => {
  return useRemoteCollaborativeFlowStore(state => ({
    onNodesChange: state.onNodesChange,
    onEdgesChange: state.onEdgesChange,
    onConnect: state.onConnect,
    addNode: state.addNode,
    updateNode: state.updateNode,
    deleteSelectedNodes: state.deleteSelectedNodes,
    selectNode: state.selectNode,
    clearAll: state.clearAll,
    connectToRoom: state.connectToRoom,
    disconnectFromRoom: state.disconnectFromRoom,
    updateCursor: state.updateCursor
  }))
}

export const useRemoteCollaborativeSelectedNode = () => {
  return useRemoteCollaborativeFlowStore(state => {
    const selectedNodeId = state.selectedNodeId
    return selectedNodeId ? state.nodes.find(node => node.id === selectedNodeId) : null
  })
}

export const useRemoteCollaborationState = () => {
  return useRemoteCollaborativeFlowStore(useShallow(state => ({
    roomId: state.roomId,
    isConnected: state.isConnected,
    participants: state.participants,
    canEdit: state.canEdit,
    isOwner: state.isOwner
  })))
}
